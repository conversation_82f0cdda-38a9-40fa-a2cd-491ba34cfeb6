{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 27554, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 27554, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 27554, "tid": 5221, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 27554, "tid": 5221, "ts": 1748529743132135, "dur": 498, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 27554, "tid": 5221, "ts": 1748529743133123, "dur": 19, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 27554, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 27554, "tid": 1, "ts": 1748529740495536, "dur": 11663, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748529740507204, "dur": 115787, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748529740622994, "dur": 112348, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 27554, "tid": 5221, "ts": 1748529743133144, "dur": 187, "ph": "X", "name": "", "args": {}}, {"pid": 27554, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740495276, "dur": 20531, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740515809, "dur": 2614292, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740516025, "dur": 3179, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740519207, "dur": 524, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740519732, "dur": 24183, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740543920, "dur": 15, "ph": "X", "name": "ProcessMessages 2518", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740543937, "dur": 36831, "ph": "X", "name": "ReadAsync 2518", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740580773, "dur": 4, "ph": "X", "name": "ProcessMessages 2057", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740580778, "dur": 72364, "ph": "X", "name": "ReadAsync 2057", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740653146, "dur": 6, "ph": "X", "name": "ProcessMessages 8177", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740653154, "dur": 92, "ph": "X", "name": "ReadAsync 8177", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740653247, "dur": 1, "ph": "X", "name": "ProcessMessages 2034", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740653248, "dur": 88, "ph": "X", "name": "ReadAsync 2034", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740653338, "dur": 1, "ph": "X", "name": "ProcessMessages 2086", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740653340, "dur": 1468, "ph": "X", "name": "ReadAsync 2086", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740654811, "dur": 4, "ph": "X", "name": "ProcessMessages 8186", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740654817, "dur": 327, "ph": "X", "name": "ReadAsync 8186", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740655146, "dur": 1, "ph": "X", "name": "ProcessMessages 2571", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740655148, "dur": 2782, "ph": "X", "name": "ReadAsync 2571", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740657958, "dur": 4, "ph": "X", "name": "ProcessMessages 8173", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740657965, "dur": 24, "ph": "X", "name": "ReadAsync 8173", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740657990, "dur": 1, "ph": "X", "name": "ProcessMessages 1822", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740657992, "dur": 38, "ph": "X", "name": "ReadAsync 1822", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740658032, "dur": 1, "ph": "X", "name": "ProcessMessages 1114", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740658033, "dur": 28, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740658063, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740658065, "dur": 52, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740658120, "dur": 120, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740658241, "dur": 580, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740658823, "dur": 1, "ph": "X", "name": "ProcessMessages 2133", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740658825, "dur": 351, "ph": "X", "name": "ReadAsync 2133", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659177, "dur": 1, "ph": "X", "name": "ProcessMessages 2565", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659179, "dur": 18, "ph": "X", "name": "ReadAsync 2565", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659198, "dur": 1, "ph": "X", "name": "ProcessMessages 1560", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659200, "dur": 243, "ph": "X", "name": "ReadAsync 1560", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659444, "dur": 1, "ph": "X", "name": "ProcessMessages 2700", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659455, "dur": 90, "ph": "X", "name": "ReadAsync 2700", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659547, "dur": 17, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659579, "dur": 30, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659612, "dur": 27, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659642, "dur": 17, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659661, "dur": 37, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740659701, "dur": 347, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660049, "dur": 4, "ph": "X", "name": "ProcessMessages 7369", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660054, "dur": 21, "ph": "X", "name": "ReadAsync 7369", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660077, "dur": 18, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660098, "dur": 40, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660140, "dur": 72, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660214, "dur": 35, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660250, "dur": 1, "ph": "X", "name": "ProcessMessages 1469", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660252, "dur": 208, "ph": "X", "name": "ReadAsync 1469", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660461, "dur": 2, "ph": "X", "name": "ProcessMessages 1840", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660464, "dur": 50, "ph": "X", "name": "ReadAsync 1840", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660516, "dur": 19, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660538, "dur": 18, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740660558, "dur": 524, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661084, "dur": 4, "ph": "X", "name": "ProcessMessages 8164", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661088, "dur": 24, "ph": "X", "name": "ReadAsync 8164", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661115, "dur": 17, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661135, "dur": 19, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661156, "dur": 66, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661225, "dur": 28, "ph": "X", "name": "ReadAsync 776", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661255, "dur": 6, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661261, "dur": 29, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661292, "dur": 31, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661325, "dur": 1, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661326, "dur": 18, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661346, "dur": 18, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661371, "dur": 49, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661421, "dur": 1, "ph": "X", "name": "ProcessMessages 1501", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661423, "dur": 30, "ph": "X", "name": "ReadAsync 1501", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740661455, "dur": 9723, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740671388, "dur": 5, "ph": "X", "name": "ProcessMessages 8114", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740671402, "dur": 54, "ph": "X", "name": "ReadAsync 8114", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740673869, "dur": 2, "ph": "X", "name": "ProcessMessages 3002", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740673872, "dur": 43, "ph": "X", "name": "ReadAsync 3002", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740673916, "dur": 3, "ph": "X", "name": "ProcessMessages 8151", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740673920, "dur": 338, "ph": "X", "name": "ReadAsync 8151", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674260, "dur": 4, "ph": "X", "name": "ProcessMessages 7464", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674264, "dur": 20, "ph": "X", "name": "ReadAsync 7464", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674287, "dur": 19, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674309, "dur": 19, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674330, "dur": 25, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674358, "dur": 15, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674375, "dur": 43, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674421, "dur": 35, "ph": "X", "name": "ReadAsync 1138", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674458, "dur": 32, "ph": "X", "name": "ReadAsync 1347", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674492, "dur": 46, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674540, "dur": 19, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674561, "dur": 37, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674600, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674624, "dur": 172, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674802, "dur": 2, "ph": "X", "name": "ProcessMessages 4455", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674805, "dur": 17, "ph": "X", "name": "ReadAsync 4455", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674824, "dur": 21, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674848, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674873, "dur": 41, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674915, "dur": 1, "ph": "X", "name": "ProcessMessages 1215", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740674917, "dur": 335, "ph": "X", "name": "ReadAsync 1215", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675253, "dur": 1, "ph": "X", "name": "ProcessMessages 2109", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675424, "dur": 26, "ph": "X", "name": "ReadAsync 2109", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675451, "dur": 3, "ph": "X", "name": "ProcessMessages 8145", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675455, "dur": 19, "ph": "X", "name": "ReadAsync 8145", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675477, "dur": 320, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675798, "dur": 3, "ph": "X", "name": "ProcessMessages 8124", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675802, "dur": 29, "ph": "X", "name": "ReadAsync 8124", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675833, "dur": 18, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675854, "dur": 40, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675896, "dur": 18, "ph": "X", "name": "ReadAsync 1271", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675917, "dur": 27, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740675946, "dur": 164, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676112, "dur": 65, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676180, "dur": 26, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676208, "dur": 23, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676233, "dur": 29, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676263, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676265, "dur": 50, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676315, "dur": 1, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676317, "dur": 166, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676488, "dur": 2, "ph": "X", "name": "ProcessMessages 3922", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676491, "dur": 15, "ph": "X", "name": "ReadAsync 3922", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676508, "dur": 191, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676701, "dur": 17, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676864, "dur": 18, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740676884, "dur": 152, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740677039, "dur": 352, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740677392, "dur": 4, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740677398, "dur": 43, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740677443, "dur": 197, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740677641, "dur": 49, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740677692, "dur": 243, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740677937, "dur": 1, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740677938, "dur": 336, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740678277, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740678279, "dur": 18, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740678300, "dur": 310, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740678612, "dur": 323, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740678938, "dur": 153, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740679093, "dur": 51, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740679146, "dur": 345, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740679493, "dur": 51, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740679546, "dur": 185, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740679733, "dur": 447, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740680182, "dur": 26, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740680210, "dur": 518, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740680731, "dur": 572, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740681306, "dur": 123, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740681430, "dur": 512, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740681944, "dur": 18, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740681964, "dur": 726, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740682693, "dur": 407, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740683105, "dur": 32, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740683142, "dur": 501, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740683645, "dur": 27, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740683675, "dur": 566, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740684243, "dur": 125, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740684371, "dur": 23, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740684396, "dur": 23, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740684634, "dur": 33, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740684670, "dur": 486, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740685158, "dur": 256, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740685416, "dur": 339, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740685757, "dur": 160, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740685919, "dur": 1, "ph": "X", "name": "ProcessMessages 1277", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740685920, "dur": 923, "ph": "X", "name": "ReadAsync 1277", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740686846, "dur": 58, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740686904, "dur": 6, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740686911, "dur": 131, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740687296, "dur": 18, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740687315, "dur": 1, "ph": "X", "name": "ProcessMessages 2070", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740687317, "dur": 337, "ph": "X", "name": "ReadAsync 2070", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740687656, "dur": 188, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740687847, "dur": 534, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740688383, "dur": 14, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740688399, "dur": 529, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740688931, "dur": 103, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740689036, "dur": 29, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740689068, "dur": 17, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740689087, "dur": 588, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740689678, "dur": 26, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740689706, "dur": 548, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740690256, "dur": 117, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740690375, "dur": 498, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740690876, "dur": 903, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740691780, "dur": 1, "ph": "X", "name": "ProcessMessages 1294", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740691781, "dur": 657, "ph": "X", "name": "ReadAsync 1294", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740693638, "dur": 442, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740694081, "dur": 1, "ph": "X", "name": "ProcessMessages 2178", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740694083, "dur": 17, "ph": "X", "name": "ReadAsync 2178", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740694102, "dur": 19014, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740713121, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740713123, "dur": 384, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740713509, "dur": 4, "ph": "X", "name": "ProcessMessages 8150", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740713514, "dur": 47, "ph": "X", "name": "ReadAsync 8150", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740718312, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740718468, "dur": 18890, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740737361, "dur": 5, "ph": "X", "name": "ProcessMessages 8177", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740737367, "dur": 35, "ph": "X", "name": "ReadAsync 8177", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740737404, "dur": 3, "ph": "X", "name": "ProcessMessages 8181", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740737408, "dur": 21, "ph": "X", "name": "ReadAsync 8181", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740737432, "dur": 21, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740737455, "dur": 39, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740737497, "dur": 567, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740738065, "dur": 1, "ph": "X", "name": "ProcessMessages 2298", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740738068, "dur": 280, "ph": "X", "name": "ReadAsync 2298", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740738350, "dur": 21, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740738373, "dur": 22, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740738397, "dur": 18, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740738418, "dur": 18, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740738438, "dur": 470, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740738910, "dur": 190, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740739103, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740739125, "dur": 279, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740739407, "dur": 179, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740739588, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740739589, "dur": 72, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740739664, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740739758, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740739815, "dur": 102, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740739919, "dur": 95, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740016, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740092, "dur": 80, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740174, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740216, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740305, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740389, "dur": 8, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740398, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740468, "dur": 240, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740710, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740711, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740732, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740757, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740818, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740820, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740908, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740740964, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741047, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741067, "dur": 10, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741079, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741282, "dur": 67, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741352, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741422, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741454, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741477, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741549, "dur": 79, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741630, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741692, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741747, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741866, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740741910, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740742011, "dur": 137, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740742149, "dur": 173, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740742323, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740742615, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740742616, "dur": 21, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740742639, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740742640, "dur": 220, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740742911, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740742936, "dur": 105, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740743044, "dur": 138, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740743184, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740743356, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740743411, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740743432, "dur": 542, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740743976, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740743978, "dur": 24, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740744004, "dur": 650, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740744655, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740744656, "dur": 373, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745031, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745033, "dur": 315, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745349, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745350, "dur": 51, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745402, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745404, "dur": 67, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745474, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745649, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745692, "dur": 92, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745785, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745792, "dur": 62, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745857, "dur": 18, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745877, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745899, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745968, "dur": 4, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740745974, "dur": 189, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740746164, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740746166, "dur": 29, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740746197, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740746368, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740746370, "dur": 22, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740746393, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740746580, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740746698, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740746706, "dur": 680, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740747387, "dur": 3, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740747391, "dur": 58, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740747451, "dur": 791, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740748244, "dur": 3781, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740752028, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740752170, "dur": 430, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740752602, "dur": 92, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740752697, "dur": 582, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740753281, "dur": 982, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740754273, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740754396, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740754446, "dur": 452, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740754921, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740754923, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740754965, "dur": 709, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740755676, "dur": 343, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740756021, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740756283, "dur": 263, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740756549, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740756602, "dur": 152, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740756757, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740756796, "dur": 443, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740757241, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740757338, "dur": 1655, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740758996, "dur": 607, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740759609, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740759691, "dur": 345, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740760039, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740760109, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740760174, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740760442, "dur": 684, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740761127, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740761130, "dur": 115, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740761248, "dur": 1009, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740762259, "dur": 322, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740762583, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740762674, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740762742, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740762771, "dur": 172, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740762950, "dur": 576, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740763528, "dur": 303, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740764008, "dur": 204, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740764214, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740764354, "dur": 712, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740765068, "dur": 861, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740765931, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740766012, "dur": 297, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740766320, "dur": 1143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740767465, "dur": 135, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740767603, "dur": 271, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740767875, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740767962, "dur": 384, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740768350, "dur": 401, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740768752, "dur": 1965, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740770719, "dur": 817, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740771539, "dur": 681, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740772222, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740772251, "dur": 994, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740773247, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740773451, "dur": 343, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740773795, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740773802, "dur": 506, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740774310, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740774476, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740774497, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740774588, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740774677, "dur": 157, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740774835, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740775008, "dur": 700, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740775710, "dur": 133, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740775847, "dur": 277, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740776126, "dur": 362, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740776491, "dur": 860, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740777354, "dur": 788, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740778144, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740778192, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740778274, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740778367, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740778371, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740778463, "dur": 193, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740778658, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740778723, "dur": 172, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740778903, "dur": 1030, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740779935, "dur": 803, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740780741, "dur": 419, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740781162, "dur": 398, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740781562, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740781724, "dur": 292, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740782024, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740782101, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740782225, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740782227, "dur": 315, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740782544, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740782672, "dur": 1041, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740783715, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740783847, "dur": 231, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740784261, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740784308, "dur": 573, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740784883, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740785032, "dur": 1032, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740786066, "dur": 680, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740786749, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740786851, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740787120, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740787237, "dur": 1816, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740789057, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740789059, "dur": 320, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740789390, "dur": 9699, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740799094, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740799097, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740799135, "dur": 811, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740799949, "dur": 1065, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740801016, "dur": 865, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740801884, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740802070, "dur": 2253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740804329, "dur": 151, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740804483, "dur": 642, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740805127, "dur": 563, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740805693, "dur": 246, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740805942, "dur": 432, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740806376, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740806504, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740806596, "dur": 226, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740806825, "dur": 123, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740806951, "dur": 376, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740807350, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740807414, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740807442, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740807552, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740807554, "dur": 233, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740807789, "dur": 179, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740807969, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740808036, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740808130, "dur": 282, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740808414, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740808489, "dur": 419, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740808911, "dur": 115, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740809028, "dur": 270, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740809298, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740809301, "dur": 72951, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740882259, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740882262, "dur": 45, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740882316, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740882691, "dur": 31, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740882724, "dur": 340, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740883067, "dur": 31, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740883101, "dur": 19, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740883121, "dur": 179, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740883301, "dur": 3241, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740886543, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740886545, "dur": 548, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740887096, "dur": 210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740887308, "dur": 1060, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740888372, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740888443, "dur": 172, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740888618, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740888900, "dur": 718, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740889822, "dur": 450, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740890274, "dur": 1136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740891413, "dur": 468, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740891885, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740891919, "dur": 327, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740892252, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740892256, "dur": 586, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740892845, "dur": 809, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740893657, "dur": 751, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740894410, "dur": 555, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740894967, "dur": 1990, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740896960, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740897100, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740897238, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740897298, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740897299, "dur": 310, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740897616, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740897620, "dur": 1332, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740898958, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740898962, "dur": 759, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740899724, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740899823, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740899914, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740899943, "dur": 2099, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740902045, "dur": 254, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740902302, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740902335, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740902415, "dur": 1586, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740904003, "dur": 421, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740904427, "dur": 330, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740904758, "dur": 542, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740905303, "dur": 501, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740905810, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740905815, "dur": 1316, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907134, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907226, "dur": 111, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907340, "dur": 315, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907657, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907701, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907725, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907757, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907946, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907969, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740907992, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908016, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908087, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908132, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908191, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908243, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908467, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908469, "dur": 35, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908506, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908553, "dur": 197, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908753, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908777, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908822, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908845, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908861, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740908939, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909026, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909052, "dur": 46, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909101, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909129, "dur": 243, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909374, "dur": 60, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909442, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909481, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909512, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909561, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909627, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909652, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909757, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909807, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740909832, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740910084, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740910087, "dur": 149, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740910239, "dur": 115, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740910355, "dur": 7, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740910363, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740910394, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529740910448, "dur": 1940999, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529742851472, "dur": 63, "ph": "X", "name": "ProcessMessages 1842", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529742851539, "dur": 6442, "ph": "X", "name": "ReadAsync 1842", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529742858019, "dur": 6, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529742858028, "dur": 2937, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529742860975, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529742861011, "dur": 156435, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743017450, "dur": 16, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743017468, "dur": 2660, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743020132, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743020135, "dur": 53743, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743073887, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743073889, "dur": 41, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743073934, "dur": 40, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743073977, "dur": 54, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743074034, "dur": 40, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743074075, "dur": 18, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743074094, "dur": 2962, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743077066, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743077068, "dur": 769, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743077839, "dur": 30, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743077870, "dur": 42045, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743119921, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743119923, "dur": 218, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743120144, "dur": 24, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743120170, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743120198, "dur": 328, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743120527, "dur": 16, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743120544, "dur": 2597, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743123142, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743123144, "dur": 278, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743123424, "dur": 35, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743123461, "dur": 452, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743123918, "dur": 303, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 27554, "tid": 25769803776, "ts": 1748529743124223, "dur": 5866, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 27554, "tid": 5221, "ts": 1748529743133333, "dur": 1347, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 27554, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 27554, "tid": 21474836480, "ts": 1748529740493225, "dur": 242135, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 27554, "tid": 21474836480, "ts": 1748529740735362, "dur": 391, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 27554, "tid": 5221, "ts": 1748529743134681, "dur": 16, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 27554, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 27554, "tid": 17179869184, "ts": 1748529740388768, "dur": 2741714, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 27554, "tid": 17179869184, "ts": 1748529740390051, "dur": 101465, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 27554, "tid": 17179869184, "ts": 1748529743130496, "dur": 1004, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 27554, "tid": 17179869184, "ts": 1748529743130864, "dur": 75, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 27554, "tid": 5221, "ts": 1748529743134699, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748529740515781, "dur": 8842, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748529740524635, "dur": 19799, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748529740544476, "dur": 138, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748529740544615, "dur": 99, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748529740545225, "dur": 108855, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748529740654601, "dur": 1018, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748529740655706, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748529740655841, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748529740656240, "dur": 2614, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748529740662681, "dur": 9387, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748529740672332, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748529740672843, "dur": 2009, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748529740676254, "dur": 133, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748529740701789, "dur": 12346, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748529740718647, "dur": 779, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748529740721456, "dur": 16876, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748529740544719, "dur": 195070, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748529740739797, "dur": 2384576, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748529743124540, "dur": 653, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748529740544671, "dur": 195133, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740740036, "dur": 591, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1748529740740627, "dur": 1900, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1748529740742527, "dur": 693, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1748529740739808, "dur": 3412, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740743221, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740743355, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748529740743618, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740743675, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748529740743878, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748529740744262, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740744325, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740744394, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740744453, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748529740744874, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748529740745740, "dur": 223, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748529740745963, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740746037, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740746116, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748529740746649, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748529740747061, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740747297, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740747355, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740747448, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740747506, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748529740748072, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740748918, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740749569, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740750234, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740750862, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740751474, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740752091, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740752603, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740753025, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740753453, "dur": 3798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740757252, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740757691, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740758759, "dur": 4528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740763288, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740763676, "dur": 1199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740764910, "dur": 1839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740766750, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740766931, "dur": 1347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740768311, "dur": 17240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740785552, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740785849, "dur": 2233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740788098, "dur": 16805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740804904, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740804976, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_95D2DEE1BA1172D7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740805107, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740805363, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740806842, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740807006, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740807184, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740807275, "dur": 929, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740808204, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748529740808474, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740808868, "dur": 76212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740885081, "dur": 2218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740887299, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740887371, "dur": 5045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740892417, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740892486, "dur": 2815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740895314, "dur": 2823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740898137, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740898240, "dur": 4706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740902946, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740903008, "dur": 6770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748529740909779, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740909910, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740910161, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740910440, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740910583, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740910713, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748529740910821, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740911130, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748529740911356, "dur": 2213027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740544672, "dur": 195139, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740739816, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748529740740000, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740740271, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740740435, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740740491, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740740681, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740740766, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740740823, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740740994, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740741073, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740741174, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740741284, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740741367, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740741479, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740741562, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740741682, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740741757, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740741858, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740742022, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740742094, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740742237, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740742309, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740742494, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740742640, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740742762, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748529740742896, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740742974, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740743108, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740743194, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740743334, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748529740743489, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748529740743705, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748529740743882, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748529740744059, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748529740744462, "dur": 1254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748529740745971, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740746045, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740746123, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740746239, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748529740746829, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740746895, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748529740747074, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740747294, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740747354, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740747439, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740747731, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748529740748065, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740748631, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740749289, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740749953, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740750575, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740751190, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740751793, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740752466, "dur": 1176, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Overrides/ColorCurvesEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748529740752466, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740754356, "dur": 1895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740756251, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740756383, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740757481, "dur": 962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740758466, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740760734, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740760951, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740761006, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740762134, "dur": 1415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740763565, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740764582, "dur": 3789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740768371, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740768486, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740769509, "dur": 2700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740772209, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740772417, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740775103, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740775193, "dur": 1766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740777019, "dur": 5980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740783000, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740783074, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_A9B74A479D61B912.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740783168, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740783552, "dur": 3267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740786820, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740786954, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740787940, "dur": 2274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740790214, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740790271, "dur": 9034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740799306, "dur": 1325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740800714, "dur": 2008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740802751, "dur": 4468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740807219, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740807467, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740807631, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740808165, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740808345, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748529740808517, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740809009, "dur": 76086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740885095, "dur": 2544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740887640, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740887699, "dur": 5030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740892730, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740892821, "dur": 2889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740895739, "dur": 4009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740899748, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740899821, "dur": 3418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740903239, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529740903303, "dur": 1617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740904957, "dur": 1498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740906497, "dur": 4604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529740911135, "dur": 1943879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529742856337, "dur": 407, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748529742855018, "dur": 5520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529742861439, "dur": 305, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529743074706, "dur": 297, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748529742862304, "dur": 212708, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748529743077848, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748529743077843, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748529743077913, "dur": 640, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748529743078556, "dur": 45869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740544678, "dur": 195142, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740739824, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748529740740035, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740740373, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740740429, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740740492, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740740576, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740740675, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740740731, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740740834, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740740924, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740741006, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740741062, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740741187, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740741271, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740741346, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740741600, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740741732, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740741855, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740741970, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740742115, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740742297, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740742409, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740742466, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740742590, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740742652, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740742795, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740742873, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740742925, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740743018, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740743121, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740743273, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740743363, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748529740743616, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748529740744253, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740744311, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748529740744542, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748529740745032, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748529740745783, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748529740745877, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748529740746067, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740746177, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740746277, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748529740746785, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740747083, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740747306, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740747393, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740747471, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748529740748109, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740748655, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740749328, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740749998, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740750618, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740751231, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740751837, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740752522, "dur": 556, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Camera/UniversalRenderPipelineCameraUI.Output.Drawers.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748529740752522, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740753414, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740753535, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740753622, "dur": 1069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740754724, "dur": 2431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740757156, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740757352, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740758213, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740759064, "dur": 2023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740761087, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740761317, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740762772, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740763837, "dur": 1376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740765250, "dur": 3234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740768485, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740768815, "dur": 3633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740772448, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740772633, "dur": 3434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740776067, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740776385, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740776446, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740776611, "dur": 1572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740778220, "dur": 4264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740782484, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740782595, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740783365, "dur": 4298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740787663, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740787986, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_C5B7873C632B963A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740788084, "dur": 914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740788998, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740789690, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740790518, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740791311, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740792045, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740792737, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740793554, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740794368, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740795139, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740795404, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740795611, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740795799, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740796013, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740796242, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740796467, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740796978, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740797568, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740798066, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740798412, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740798756, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740798956, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740799562, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740800200, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740801025, "dur": 1889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748529740802914, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740802969, "dur": 3311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740806280, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740806847, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740807328, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740808258, "dur": 76819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740885080, "dur": 2932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740888012, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740888066, "dur": 1275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740889382, "dur": 1726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740891160, "dur": 2385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740893583, "dur": 2244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740895827, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740895881, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740898184, "dur": 2611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740900843, "dur": 2330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740903173, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740903241, "dur": 2979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740906253, "dur": 4521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748529740910852, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529740911260, "dur": 2166585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748529743077909, "dur": 656, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748529743078566, "dur": 45834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740544689, "dur": 195141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740739836, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740740366, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740740478, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740740598, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740740672, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740740812, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740741028, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740741109, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740741224, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740741323, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740741493, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740741575, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740741703, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740741778, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740741893, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740741973, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740742051, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740742117, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740742250, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740742326, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740742406, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740742479, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740742596, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740742722, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740742928, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740743151, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740743283, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740743437, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748529740743581, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740743709, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740744249, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740744825, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740745670, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740745835, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740746032, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740746114, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740746791, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740746845, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740746947, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740747289, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740747378, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740747453, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740747513, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740747670, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740747915, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748529740748287, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740748344, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740748989, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740749669, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740750294, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740750944, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740751578, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740752196, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740752613, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740753242, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740753474, "dur": 2027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740755501, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740755858, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740756904, "dur": 3880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740760785, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740761074, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740761989, "dur": 1441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740763477, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748529740764661, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740767134, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740767212, "dur": 5119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748529740772331, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740772527, "dur": 1910, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740883067, "dur": 968, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740774727, "dur": 109321, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1748529740885077, "dur": 4700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740889777, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740889839, "dur": 3165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740893030, "dur": 2801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740895831, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740895927, "dur": 1946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740897873, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529740897928, "dur": 3571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740901510, "dur": 2497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740904053, "dur": 1237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740905330, "dur": 5914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748529740911264, "dur": 2212730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748529743124036, "dur": 287, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748529743124326, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740544697, "dur": 195142, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740739843, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740740374, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740740468, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740740535, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740740699, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740740839, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740741010, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740741140, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740741224, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740741365, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740741584, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740741643, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740741768, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740741898, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740741980, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740742081, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740742138, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740742264, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740742372, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740742504, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740742643, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740742734, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740742857, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740742968, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740743084, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740743143, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740743214, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748529740743387, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740743459, "dur": 1285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740744745, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740744958, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748529740745279, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748529740745935, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740746059, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740746171, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748529740746371, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748529740746798, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748529740747052, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740747328, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740747465, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740747525, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748529740747722, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748529740748023, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748529740748306, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740748362, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740748998, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740749689, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740750310, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740750961, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740751588, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740752209, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740752804, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740753142, "dur": 1230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740754386, "dur": 12085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740766471, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740766906, "dur": 1355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740768298, "dur": 12188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740780488, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740780772, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740780832, "dur": 1571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740782447, "dur": 4995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740787442, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740787612, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_059BC5464FF14C2B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740787663, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740787728, "dur": 2138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740789904, "dur": 9407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740799312, "dur": 605, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740799954, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740800030, "dur": 1872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740801938, "dur": 4392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740806331, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740806599, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740807276, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740808248, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748529740808550, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740808934, "dur": 76155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740885090, "dur": 2773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740887910, "dur": 1354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740889307, "dur": 2868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740892175, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740892238, "dur": 2813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740895052, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740895187, "dur": 3429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740898617, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748529740898674, "dur": 1975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740900697, "dur": 4974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740905707, "dur": 1475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740907230, "dur": 4103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748529740911352, "dur": 2213022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740544703, "dur": 195164, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740739873, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740740356, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740740477, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740740654, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740740707, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740740805, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740740883, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740741029, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740741173, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740741239, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740741355, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740741510, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740741591, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740741678, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740741776, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740741899, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740742003, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740742092, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740742161, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740742280, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740742387, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740742457, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740742533, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740742648, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740742790, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748529740742928, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740742984, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740743410, "dur": 1498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740744908, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740745121, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740745176, "dur": 2401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740747592, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740747994, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740749082, "dur": 3650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740752732, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740752984, "dur": 1111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740754095, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740754171, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740756809, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740757156, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740758001, "dur": 2418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740760419, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740760582, "dur": 13267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740773849, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740774137, "dur": 4713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740778851, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740779093, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740779216, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740779306, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740779413, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740779538, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740779693, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740780493, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740781311, "dur": 1639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740782996, "dur": 4143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740787139, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740787427, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740787503, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740787564, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740788445, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740789332, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740790022, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740790765, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740791646, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740792416, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740793126, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740793981, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740794766, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740795426, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740795633, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740795832, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740796046, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740796278, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740796790, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740797207, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740797776, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740798062, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740798341, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740798796, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740798846, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740798912, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740798980, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740799593, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740800220, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740801059, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740801714, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740802396, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740802933, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740803092, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740803194, "dur": 120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740803315, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740803759, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740804375, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740804867, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740805224, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740805501, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740805949, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740806545, "dur": 102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740806859, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740807287, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740808256, "dur": 1469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740809726, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748529740809837, "dur": 75268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740885106, "dur": 3085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740888244, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740889561, "dur": 3008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740892569, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740892623, "dur": 1879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740894538, "dur": 3424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740897963, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740898039, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740900694, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748529740900782, "dur": 3190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740904001, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740905356, "dur": 5929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748529740911304, "dur": 2213107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740544709, "dur": 195183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740739895, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740740266, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740740445, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740740572, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740740647, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740740711, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740740796, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740740889, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740740984, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740741053, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740741129, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740741227, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740741309, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740741473, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740741593, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740741668, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740741727, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740741803, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740741945, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740742106, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740742247, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740742333, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740742475, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740742592, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740742657, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740742813, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740743131, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740743205, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740743287, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740743524, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740743742, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748529740743936, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748529740744395, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740744790, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748529740744910, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740745685, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740745756, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740745933, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740746007, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740746072, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740746175, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740746261, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740746319, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748529740746610, "dur": 1175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740747799, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748529740748261, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740748904, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740749564, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740750210, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740750835, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740751348, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740751979, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740752581, "dur": 1073, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/AssetPostProcessors/AutodeskInteractiveMaterialImport.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748529740752581, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740754339, "dur": 5020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529740759359, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740759470, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740759571, "dur": 10976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529740770548, "dur": 898, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740771471, "dur": 2777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529740774249, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740774342, "dur": 981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740775584, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740775654, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740775716, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_AAF6E30B590FA4FC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740775835, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_94629FB7B2A83F68.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740775924, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740776096, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740776223, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740776321, "dur": 1015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740777369, "dur": 4282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529740781651, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740781846, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740782906, "dur": 1618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529740784525, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740784677, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740785305, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740786141, "dur": 1944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740788102, "dur": 19087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529740807191, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740807569, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740807648, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529740808101, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740808187, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740808396, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529740809150, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740809313, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740809377, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529740809583, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740809720, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748529740809854, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529742851292, "dur": 58, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529740810169, "dur": 2041233, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529742857441, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748529742855006, "dur": 2756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529742858112, "dur": 180, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529742858296, "dur": 66, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529742859619, "dur": 158696, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748529743019007, "dur": 1232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748529743020761, "dur": 210, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529743120733, "dur": 434, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748529743021114, "dur": 100061, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748529743124047, "dur": 293, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748529740544715, "dur": 195183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740739901, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740740423, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740740627, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740740686, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740740825, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740741018, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740741116, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740741198, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740741277, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740741357, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740741489, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740741567, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740741697, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740741776, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740741879, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740742015, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740742087, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740742180, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740742264, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740742394, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740742452, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740742567, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740742645, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740742802, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740742880, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740742976, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740743149, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740743220, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740743388, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748529740743528, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740743626, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748529740743702, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740743902, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748529740744254, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740744315, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_18DD15F206BB0EF4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740744405, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740744819, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748529740744938, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740745269, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740745827, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740745934, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740746015, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740746088, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740746299, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748529740746807, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740747091, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740747218, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740747272, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740747666, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748529740748079, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740748628, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740749292, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740749958, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740750586, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740751214, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740751818, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740752503, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740752907, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740753329, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740753495, "dur": 1555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740755050, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740755320, "dur": 1132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740756485, "dur": 2504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740758989, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740759165, "dur": 2636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740761802, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740762039, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740763589, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740764421, "dur": 1256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740765695, "dur": 3421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740769116, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740769246, "dur": 3709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740772955, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740773153, "dur": 3214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740776367, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740776710, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_050F300D13D769AC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740776762, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740776819, "dur": 4624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740781443, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740781613, "dur": 1449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740783080, "dur": 1810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740784891, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740785006, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740785066, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740785703, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740786769, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740787719, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740788546, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740789468, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740790192, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740791030, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740791796, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740792585, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740793314, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740794168, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740794953, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740795441, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740795639, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740795840, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740796051, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740796275, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740796678, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740797181, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740797748, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740798041, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740798338, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740798949, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740799457, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740800057, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748529740801904, "dur": 4046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740805950, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740806011, "dur": 1323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740807364, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740807772, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740807847, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740808258, "dur": 76859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740885120, "dur": 2118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740887258, "dur": 4057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740891340, "dur": 3185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740894557, "dur": 3810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740898418, "dur": 2062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740900481, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740900538, "dur": 2236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740902807, "dur": 3080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740905924, "dur": 2023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748529740907949, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740908040, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740908177, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740910002, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740910224, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740910536, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740910675, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740910740, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740910865, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740911300, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748529740911359, "dur": 2213047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748529743126855, "dur": 766, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 27554, "tid": 5221, "ts": 1748529743135303, "dur": 2517, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 27554, "tid": 5221, "ts": 1748529743137841, "dur": 559, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 27554, "tid": 5221, "ts": 1748529743132637, "dur": 5789, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}