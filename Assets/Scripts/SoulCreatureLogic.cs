using UnityEngine;
using System.Collections.Generic;
using System;
using FMODUnity;
using Random = UnityEngine.Random;

public interface ISoulCreatureBoostProvider
{
    float GetFlightBoostValue();
}

public class SoulCreatureLogic : MonoBehaviour, ISoulCreatureBoostProvider
{
    // Static registry of all active SoulCreatureLogic instances
    private static readonly List<SoulCreatureLogic> ActiveInstances = new List<SoulCreatureLogic>();

    // Static method to get all active instances
    public static List<SoulCreatureLogic> GetAllActiveInstances()
    {
        return ActiveInstances;
    }

    #region Core Components and References

    // Core references
    private Transform playerTransform;
    private PlayerController playerController;
    private ParticleSystem ps;
    private CreatureMovement creatureMovement;
    private Camera mainCamera;

    // Boundary values for movement constraints
    [SerializeField] public float minY = 20;
    [SerializeField] public float maxY = 50;

    // Public access to average particle position
    [HideInInspector] public Vector3 averageParticlePosition;

    [Header("Particle System Settings")]
    [Tooltip("Minimum number of max particles")]
    [SerializeField] private int minMaxParticles = 200;

    [Tooltip("Maximum number of max particles")]
    [SerializeField] private int maxMaxParticles = 1000;

    // Color settings for ocean creatures
    [Header("Ocean Creature Color Settings")]
    [Tooltip("Whether this is an ocean-type creature that should have random colors")]
    public bool isOceanCreature = false;

    [Tooltip("Minimum color hue (0-1)")]
    [Range(0f, 1f)] public float minHue = 0.5f;

    [Tooltip("Maximum color hue (0-1)")]
    [Range(0f, 1f)] public float maxHue = 0.7f;

    [Tooltip("Saturation for the color (0-1)")]
    [Range(0f, 1f)] public float colorSaturation = 0.8f;

    [Tooltip("Brightness for the color (0-1)")]
    [Range(0f, 1f)] public float colorBrightness = 0.8f;

    // Render queue settings for ocean creatures
    [Header("Render Queue Settings")]
    [Tooltip("Whether to use the centralized render queue system in GameManager")]
    public bool useCentralizedRenderQueue = true;

    #endregion

    #region Module Toggles

    [Header("Module Toggles")]
    [Tooltip("Enable/disable particle following behavior")]
    public bool enableParticleFollowing = true;

    #endregion

    #region Particle Following Settings

    [Header("Particle Following Settings")]
    [Tooltip("Minimum value for minimum speed particles can move at")]
    [SerializeField] private float minMinSpeed = 1f;

    [Tooltip("Maximum value for minimum speed particles can move at")]
    [SerializeField] private float maxMinSpeed = 5f;

    [Tooltip("Current randomized minimum speed")]
    [SerializeField] private float minSpeed = 1f;

    [Tooltip("Minimum value for maximum speed particles can move at")]
    [SerializeField] private float minMaxSpeed = 10f;

    [Tooltip("Maximum value for maximum speed particles can move at")]
    [SerializeField] private float maxMaxSpeed = 20f;

    [Tooltip("Current randomized maximum speed")]
    [SerializeField] private float maxSpeed = 10f;

    [Tooltip("Rate at which particle speed interpolates to desired speed")]
    [SerializeField] private float lerpFactor = 2f;

    [Tooltip("Distance from target where particles start following")]
    [SerializeField] private float followThreshold = 1f;

    [Tooltip("Distance from target where particles stop following")]
    [SerializeField] private float stopThreshold = 0.3f;

    [Tooltip("Minimum value for maximum distance at which speed scaling reaches its maximum effect")]
    [SerializeField] private float minMaxDistance = 3f;

    [Tooltip("Maximum value for maximum distance at which speed scaling reaches its maximum effect")]
    [SerializeField] private float maxMaxDistance = 20f;

    [Tooltip("Current randomized maximum distance")]
    [SerializeField] private float maxDistance = 5f;

    [Header("Particle Update Rate")]
    [Tooltip("How often (in seconds) to update particle movement. Lower is smoother, higher is more performant.")]
    [SerializeField] private float particleUpdateInterval = 0.01f;
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;
    private float particleUpdateTimer = 0f;

    [Header("Distance-Based Performance Settings")]
    [Tooltip("Distance from player where performance optimization starts")]
    [SerializeField] private float performanceOptimizationStartDistance = 10f;
    [Tooltip("Distance from player where maximum performance optimization is applied")]
    [SerializeField] private float performanceOptimizationMaxDistance = 30f;
    [Tooltip("Particle update interval for creatures at maximum distance")]
    [SerializeField] private float maxDistanceParticleUpdateInterval = 0.1f;
    [Tooltip("Render queue update interval for creatures at maximum distance")]
    [SerializeField] private float maxDistanceRenderQueueUpdateInterval = 1f;

    // Performance optimization variables
    private float currentParticleUpdateInterval;

    // Render queue management
    private bool materialsRegistered = false;

    // Particle following internal variables
    private Mesh shapeMesh;
    private ParticleSystem.Particle[] particlesArray;
    private ParticleData[] particlesData;
    private float[] triangleAreas;
    private float totalArea;

    private struct ParticleData
    {
        public Vector3 TargetLocalPos;
        public float DesiredSpeed;
        public float CurrentSpeed;
        public float Timer;
        public bool IsFollowing;
        public bool initialized;
        public Vector3 Velocity;
    }

    #endregion

    [Header("Flight Boost")]
    [Tooltip("Amount of flight boost this soul creature provides to the player")]
    public float flightBoostValue = 6f;

    #region Audio and Gravity Control Settings

    // Audio component reference

    [Header("Gravity Control Settings")]
    [Tooltip("Number of particle collisions required to disable player gravity")]
    [SerializeField] private int particleCollisionsToDisableGravity = 10;

    [Tooltip("Time in seconds after which gravity is re-enabled if no particle collisions occur")]
    [SerializeField] private float gravityReenableDelay = 1.0f;

    [Tooltip("Enable debug logging for gravity control")]
    [SerializeField] private bool debugGravityControl = false;

    // Gravity control variables
    private int currentParticleCollisionCount = 0;
    private float lastParticleCollisionTime = -Mathf.Infinity;
    private bool isGravityDisabled = false;

    #endregion

    #region Unity Lifecycle Methods

    private void Awake()
    {
        if (enableParticleFollowing)
        {
            InitializeParticleSystem();
        }
    }

    private void OnEnable()
    {
        // Register this instance in the static list
        if (!ActiveInstances.Contains(this))
        {
            ActiveInstances.Add(this);
        }
    }

    private void OnDisable()
    {
        // Remove this instance from the static list
        ActiveInstances.Remove(this);

        // Clean up gravity control
        CleanupGravityControl();
    }

    private void Start()
    {
        // Get references from GameManager
        if (GameManager.Instance != null)
        {
            playerTransform = GameManager.Instance.player.transform;
            playerController = GameManager.Instance.player;
        }
        else
        {
            //            Debug.LogError("GameManager instance not found!");
            enabled = false;
            return;
        }

        // Get CreatureMovement component
        creatureMovement = GetComponent<CreatureMovement>();
        if (creatureMovement == null)
        {
            Debug.LogWarning($"CreatureMovement component not found on {gameObject.name}. Movement functionality will be disabled.");
        }

        // Get main camera reference
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            Debug.LogWarning("Main camera not found!");
        }

        // Debug log to verify player tag
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            //            Debug.Log($"Found player with tag 'Player': {player.name}");
        }
        else
        {
            Debug.LogWarning("No GameObject with tag 'Player' found in the scene!");
        }

        // Make sure the player from GameManager has the Player tag
        if (playerTransform != null && !playerTransform.CompareTag("Player"))
        {
            Debug.LogWarning($"Player GameObject from GameManager doesn't have the 'Player' tag! Current tag: {playerTransform.tag}");
            playerTransform.tag = "Player";
            Debug.Log("Set player tag to 'Player'");
        }

        // Initialize particle system
        ps = GetComponent<ParticleSystem>();
        if (ps == null && enableParticleFollowing)
        {
            Debug.LogError("No ParticleSystem found on this GameObject!");
            enabled = false;
            return;
        }

        // Randomize particle system max particles
        var main = ps.main;
        int randomMaxParticles = Random.Range(minMaxParticles, maxMaxParticles + 1);
        var mainModule = main;
        mainModule.maxParticles = randomMaxParticles;

        // Randomize particle following settings
        minSpeed = Random.Range(minMinSpeed, maxMinSpeed);
        maxSpeed = Random.Range(minMaxSpeed, maxMaxSpeed);
        maxDistance = Random.Range(minMaxDistance, maxMaxDistance);

        // Initialize particle following
        if (enableParticleFollowing)
        {
            particlesArray = new ParticleSystem.Particle[ps.main.maxParticles];
            PrecomputeTriangleAreas();
        }



        // Apply random color for ocean creatures
        if (isOceanCreature && ps != null)
        {
            ApplyRandomColor();
        }
    }

    private void Update()
    {
        if (playerTransform == null || playerController == null) return;

        // Calculate distance to player for performance optimization
        float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);
        UpdatePerformanceSettings(distanceToPlayer);

        // Update particle following with distance-based interval
        if (enableParticleFollowing && ps != null && shapeMesh != null)
        {
            averageParticlePosition = GetAverageParticlePosition();
            UpdateParticles();
        }

        // Register materials with render queue system once (replaces expensive per-frame updates)
        RegisterMaterialsWithRenderQueueSystem();

        // Check if we need to re-enable gravity
        CheckGravityReenabling();
    }

    private void CheckGravityReenabling()
    {
        // Only check if gravity is currently disabled by this soul creature
        if (!isGravityDisabled) return;

        // Check if enough time has passed since the last particle collision
        if (Time.time - lastParticleCollisionTime >= gravityReenableDelay)
        {
            // Reset collision count
            currentParticleCollisionCount = 0;

            // Re-enable gravity if the player controller exists
            if (playerController != null)
            {
                // We need to check if any other SoulCreatureLogic is currently disabling gravity
                bool otherCreatureDisablingGravity = false;

                // Check all other active SoulCreatureLogic objects using our static list
                foreach (SoulCreatureLogic creature in ActiveInstances)
                {
                    // Skip this creature
                    if (creature == this) continue;

                    // Check if this other creature is disabling gravity
                    if (creature.isGravityDisabled)
                    {
                        otherCreatureDisablingGravity = true;
                        break;
                    }
                }

                // Only re-enable gravity if no other creature is disabling it
                if (!otherCreatureDisablingGravity)
                {
                    playerController.SetGravityDisabled(false);

                    if (debugGravityControl)
                    {
                        Debug.Log($"Re-enabled gravity for player after no interactions with {gameObject.name} for {gravityReenableDelay} seconds");
                    }
                }
                else if (debugGravityControl)
                {
                    Debug.Log($"{gameObject.name} would re-enable gravity, but another soul creature is still disabling it");
                }

                // Mark this creature as no longer disabling gravity
                isGravityDisabled = false;
            }
        }
    }

    private void UpdatePerformanceSettings(float distanceToPlayer)
    {
        // Calculate performance optimization factor based on distance
        float optimizationFactor = 0f;
        if (distanceToPlayer > performanceOptimizationStartDistance)
        {
            optimizationFactor = Mathf.InverseLerp(
                performanceOptimizationStartDistance,
                performanceOptimizationMaxDistance,
                distanceToPlayer
            );
        }

        // Update particle update interval based on distance
        currentParticleUpdateInterval = Mathf.Lerp(
            particleUpdateInterval,
            maxDistanceParticleUpdateInterval,
            optimizationFactor
        );

        // No longer needed - render queue registration is now one-time only
    }

    /// <summary>
    /// Register materials with the centralized render queue system once during initialization.
    /// This replaces the expensive per-frame UpdateRenderQueue calls.
    /// </summary>
    private void RegisterMaterialsWithRenderQueueSystem()
    {
        if (materialsRegistered || !isOceanCreature || !useCentralizedRenderQueue || GameManager.Instance == null)
            return;

        // Get all renderers (main and child particle systems) - only once!
        ParticleSystemRenderer[] renderers = GetComponentsInChildren<ParticleSystemRenderer>();

        // Register all materials with the centralized system
        foreach (ParticleSystemRenderer renderer in renderers)
        {
            foreach (Material material in renderer.materials)
            {
                if (material != null)
                {
                    GameManager.Instance.AddDynamicRenderQueueMaterial(material);
                }
            }
        }

        materialsRegistered = true;
    }

    private void OnParticleCollision(GameObject other)
    {
        if (other.CompareTag("Player"))
        {
            // Handle gravity control
            HandleGravityControl();
        }
    }

    private void HandleGravityControl()
    {
        // Update collision count and time
        currentParticleCollisionCount++;
        lastParticleCollisionTime = Time.time;

        // Check if we've reached the threshold to disable gravity
        if (!isGravityDisabled && currentParticleCollisionCount >= particleCollisionsToDisableGravity)
        {
            if (playerController != null)
            {
                playerController.SetGravityDisabled(true);
                isGravityDisabled = true;

                if (debugGravityControl)
                {
                    Debug.Log($"Disabled gravity for player due to {gameObject.name} particle interactions");
                }
            }
        }
    }

    private void OnDestroy()
    {
        // Clean up any active audio events
        StopAllCoroutines();

        // Clean up materials from the centralized render queue system
        if (materialsRegistered && GameManager.Instance != null)
        {
            ParticleSystemRenderer[] renderers = GetComponentsInChildren<ParticleSystemRenderer>();
            foreach (ParticleSystemRenderer renderer in renderers)
            {
                foreach (Material material in renderer.materials)
                {
                    if (material != null)
                    {
                        GameManager.Instance.RemoveDynamicRenderQueueMaterial(material);
                    }
                }
            }
            materialsRegistered = false;
        }

        // Clean up gravity control
        CleanupGravityControl();
    }

    void CleanupGravityControl()
    {
        // Only do cleanup if this soul creature is currently disabling gravity
        if (isGravityDisabled && playerController != null)
        {
            // We need to check if any other SoulCreatureLogic is currently disabling gravity
            bool otherCreatureDisablingGravity = false;

            // Check all other active SoulCreatureLogic objects using our static list
            foreach (SoulCreatureLogic creature in ActiveInstances)
            {
                // Skip this creature
                if (creature == this) continue;

                // Check if this other creature is disabling gravity
                if (creature.isGravityDisabled)
                {
                    otherCreatureDisablingGravity = true;
                    break;
                }
            }

            // Only re-enable gravity if no other creature is disabling it
            if (!otherCreatureDisablingGravity)
            {
                playerController.SetGravityDisabled(false);

                if (debugGravityControl)
                {
                    Debug.Log($"Re-enabled gravity for player due to {gameObject.name} being disabled or destroyed");
                }
            }

            // Reset state
            isGravityDisabled = false;
            currentParticleCollisionCount = 0;
        }
    }

    #region ISoulCreatureBoostProvider Implementation

    float ISoulCreatureBoostProvider.GetFlightBoostValue() => flightBoostValue;

    #endregion

    #region Color Randomization

    void ApplyRandomColor()
    {
        var audio = GetComponent<SoulCreatureAudio>();
        if (audio != null)
        {
            int soundPairCount = audio.soundPairs != null ? audio.soundPairs.Count : 0;
            int soundPairIndex = audio.SelectedSoundPairIndex;
            Color color = SoulCreatureColorManager.GetColorForSoundPair(soundPairCount, soundPairIndex);
            // Apply the color to the particle system or renderer as needed
            if (ps != null)
            {
                var main = ps.main;
                main.startColor = color;
            }
        }
    }

    #endregion

    #region Particle Following Implementation

    private void InitializeParticleSystem()
    {
        ps = GetComponent<ParticleSystem>();
        if (ps.shape.shapeType != ParticleSystemShapeType.Mesh || (shapeMesh = ps.shape.mesh) == null)
        {
            enableParticleFollowing = false;
            return;
        }
        int maxParticles = ps.main.maxParticles;
        particlesArray = new ParticleSystem.Particle[maxParticles];
        particlesData = new ParticleData[maxParticles];
    }

    private void PrecomputeTriangleAreas()
    {
        if (shapeMesh == null) return;

        int[] tris = shapeMesh.triangles;
        Vector3[] verts = shapeMesh.vertices;
        int triangleCount = tris.Length / 3;
        triangleAreas = new float[triangleCount];
        totalArea = 0f;

        for (int i = 0; i < triangleCount; i++)
        {
            Vector3 v0 = verts[tris[i * 3]];
            Vector3 v1 = verts[tris[i * 3 + 1]];
            Vector3 v2 = verts[tris[i * 3 + 2]];
            float area = 0.5f * Vector3.Cross(v1 - v0, v2 - v0).magnitude;
            triangleAreas[i] = area;
            totalArea += area;
        }
    }

    private void UpdateParticles()
    {
        particleUpdateTimer += Time.deltaTime;
        if (particleUpdateTimer < currentParticleUpdateInterval)
            return;
        float step = particleUpdateTimer;
        particleUpdateTimer = 0f;

        int count = ps.GetParticles(particlesArray);
        if (particlesData == null || particlesData.Length != particlesArray.Length)
        {
            particlesData = new ParticleData[particlesArray.Length];
        }
        for (int i = 0; i < count; i++)
        {
            ref ParticleSystem.Particle particle = ref particlesArray[i];
            ref ParticleData data = ref particlesData[i];
            if (!data.initialized)
            {
                data = InitializeParticleData();
                data.initialized = true;
                data.Velocity = Vector3.zero;
            }
            UpdateParticleSpeed(ref data, step);
            Vector3 targetWorldPos = transform.TransformPoint(data.TargetLocalPos);
            float distance = Vector3.Distance(particle.position, targetWorldPos);
            data.IsFollowing = data.IsFollowing ? distance >= stopThreshold : distance > followThreshold;
            if (data.IsFollowing)
            {
                Vector3 toTarget = (targetWorldPos - particle.position);
                float dist = toTarget.magnitude;
                Vector3 desiredVelocity = Vector3.zero;
                if (dist > 0.001f)
                {
                    float speed = data.CurrentSpeed * Mathf.Min(dist / maxDistance, 1f);
                    if (dist >= maxDistance)
                    {
                        float speedScaleFactor = Mathf.Max(dist / maxDistance, 1);
                        speed = (data.CurrentSpeed + 10) * speedScaleFactor;
                    }
                    desiredVelocity = toTarget.normalized * speed;
                }
                data.Velocity = Vector3.Lerp(data.Velocity, desiredVelocity, lerpFactor * step);
                if (dist < stopThreshold * 2f)
                {
                    data.Velocity *= Mathf.Lerp(0.1f, 1f, dist / (stopThreshold * 2f));
                }
                particle.position += data.Velocity * step;
            }
            else
            {
                data.Velocity = Vector3.Lerp(data.Velocity, Vector3.zero, lerpFactor * step);
            }
        }
        ps.SetParticles(particlesArray, count);
    }

    private ParticleData InitializeParticleData()
    {
        Vector3 localPos = GetRandomMeshPoint();
        float speed = Random.Range(minSpeed, maxSpeed);
        return new ParticleData
        {
            TargetLocalPos = localPos,
            DesiredSpeed = speed,
            CurrentSpeed = speed,
            Timer = Random.Range(0f, 4f),
            IsFollowing = false,
            initialized = true,
            Velocity = Vector3.zero
        };
    }

    private void UpdateParticleSpeed(ref ParticleData data, float deltaTime)
    {
        data.Timer += deltaTime;
        if (data.Timer > 4f)
        {
            data.Timer -= 4f;
            data.DesiredSpeed = Random.Range(minSpeed, maxSpeed);
        }
        data.CurrentSpeed = Mathf.Lerp(data.CurrentSpeed, data.DesiredSpeed, lerpFactor * deltaTime);
    }

    private Vector3 GetRandomMeshPoint()
    {
        if (shapeMesh == null) return Vector3.zero;

        int[] tris = shapeMesh.triangles;
        Vector3[] verts = shapeMesh.vertices;

        float randomValue = Random.value * totalArea;
        int triIndex = 0;
        float cumulativeArea = 0f;

        for (int i = 0; i < triangleAreas.Length; i++)
        {
            cumulativeArea += triangleAreas[i];
            if (randomValue <= cumulativeArea)
            {
                triIndex = i * 3;
                break;
            }
        }

        Vector3 v0 = verts[tris[triIndex]];
        Vector3 v1 = verts[tris[triIndex + 1]];
        Vector3 v2 = verts[tris[triIndex + 2]];

        float u = Random.value, v = Random.value;
        if (u + v > 1) { u = 1 - u; v = 1 - v; }

        return v0 + u * (v1 - v0) + v * (v2 - v0);
    }

    #endregion





    private Vector3 GetAverageParticlePosition()
    {
        if (ps == null) return transform.position;
        int count = ps.particleCount;
        if (count == 0) return transform.position;
        if (particlesArray == null || particlesArray.Length < count)
            particlesArray = new ParticleSystem.Particle[ps.main.maxParticles];
        int actualCount = ps.GetParticles(particlesArray);
        Vector3 sum = Vector3.zero;
        for (int i = 0; i < actualCount; i++)
            sum += particlesArray[i].position;
        return sum / Mathf.Max(1, actualCount);
    }

    #endregion


}